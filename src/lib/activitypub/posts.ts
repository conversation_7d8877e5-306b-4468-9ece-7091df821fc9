import {
  narrowByType,
  type Notelike,
  type Question
} from '$lib/activitypub/types';
import { importEmojis } from '$lib/activitypub/emoji';
import { discoverActor } from '$lib/activitypub/actors';
import { getUrlFromAPObjectSafe, getUrlFromAPObjectRequired } from '$lib/activitypub/utils/links';
import { post as postTable } from '$lib/server/db/schema';
import { db } from '$lib/server/db';
import { fetchAndProcessAPObject, fetchAPObjectWithRetry } from '$lib/activitypub/utils/fetch';
import { ActivityPubLogs } from '$lib/activitypub/utils/logger';
import { getObjectType, isSupportedPostType } from '$lib/activitypub/utils/types';
import { getCollectionCounts } from '$lib/activitypub/utils/collections';
import { getVisibilityFromPost } from '$lib/activitypub/utils/visibility';
import { resolveQuoteUrl } from '$lib/activitypub/utils/quotes';
import { eq } from 'drizzle-orm';
import { backfillCollection } from '$lib/activitypub/backfill';

/**
 * Enhanced post import with smart authentication for private posts
 */
export async function importPostWithAuth(url: string): Promise<any> {
  ActivityPubLogs.federation.requestSent(url, 'authenticated');

  try {
    // First try with authenticated fetch (service account signature)
    const fetchResult = await fetchAPObjectWithRetry<Notelike>(url, {
      timeout: 15000
    });

    if (fetchResult.success && fetchResult.result) {
      ActivityPubLogs.federation.requestCompleted(url, 'authenticated', 'success');
      return importPost(fetchResult.result);
    }

    // If authenticated fetch failed with 404/403, it might be a private post
    if (fetchResult.statusCode === 404 || fetchResult.statusCode === 403) {
      ActivityPubLogs.security.blockedRequest(url, `Private post access denied: ${fetchResult.statusCode}`);
      throw new Error(`Cannot access private post: ${fetchResult.error} (${fetchResult.statusCode})`);
    }

    // For other errors, throw with details
    throw new Error(`Failed to fetch post: ${fetchResult.error} (${fetchResult.statusCode || 'unknown'})`);

  } catch (error) {
    ActivityPubLogs.federation.requestCompleted(url, 'authenticated', 'failed');

    if (error instanceof Error) {
      throw error;
    }

    throw new Error(`Failed to import post from ${url}: ${error}`);
  }
}

export async function importPost(post: string | Notelike) {
  if (typeof post === 'string') {
    const fetchResult = await fetchAndProcessAPObject<Notelike>(post, { timeout: 10000 });

    if (!fetchResult.success) {
      throw new Error(`Failed to fetch post: ${fetchResult.error}`);
    }

    const fetchedPost = fetchResult.data;

    // Validate that the fetched data is a valid post object
    if (!fetchedPost || typeof fetchedPost !== 'object') {
      throw new Error('Invalid post object structure received from URL');
    }

    post = fetchedPost;
  }
  const postInDB: typeof postTable.$inferInsert = await activityPubToDB(post)
  const realPostInDB = await savePostToDB(postInDB);
  if (post.replies) {
    if (post.replies instanceof URL) {
      await backfillCollection(post.replies.toString());
    } else if (post.replies.id) {
      await backfillCollection(post.replies.id.toString());
    }
  }
  return realPostInDB;
}

/**
 * Delete a post by its ActivityPub URI
 */
export async function deletePost(uri: string): Promise<boolean> {
  try {
    // Check if post exists
    const existingPost = await db.query.post.findFirst({
      where: eq(postTable.uri, uri)
    });

    if (!existingPost) {
      console.warn(`Post not found for deletion: ${uri}`);
      return false;
    }

    // Delete the post
    const result = await db.delete(postTable).where(eq(postTable.uri, uri)).returning();

    if (result.length > 0) {
      console.log(`Successfully deleted post: ${uri}`);
      return true;
    } else {
      console.warn(`Failed to delete post: ${uri}`);
      return false;
    }
  } catch (error) {
    console.error(`Error deleting post ${uri}:`, error);
    throw error;
  }
}

async function savePostToDB(post: typeof postTable.$inferInsert) {
  if (post.uri) {
    const existingPost = await db.query.post.findFirst({
      where: eq(postTable.uri, post.uri)
    });
    if (existingPost) {
      return db.update(postTable).set(post).where(eq(postTable.uri, post.uri)).returning();
    }
  }
  return db.insert(postTable).values(post).returning()
}

async function activityPubToDB(post: Notelike): Promise<typeof postTable.$inferInsert> {
  const type = getObjectType(post);

  if (!isSupportedPostType(type)) {
    throw new Error(`Unknown object type: ${type}`);
  }

  if (!post.attributedTo) {
    throw new Error('Failed to save post: no author');
  }

  const authorUrl = getUrlFromAPObjectRequired(post.attributedTo, 'attributedTo');

  const author = await discoverActor(authorUrl);
  if (!author) {
    throw new Error("Failed to save post: couldn't get author");
  }
  if (!author.id) {
    console.log('Found author:', author);
  }

  await importEmojis(post);

  if (!post.id) {
    throw new Error('Failed to save post: no id');
  }

  // Get collection counts in parallel for better performance
  const collectionResult = await getCollectionCounts(
    post.replies,
    post.likes,
    post.shares,
    { timeout: 5000, defaultValue: 0, continueOnError: true }
  );

  // Log warnings if there were errors getting collection counts
  if (collectionResult.errors.length > 0) {
    console.warn('Collection count errors:', collectionResult.errors);
  }

  return {
    userId: author.id,
    uri: post.id.toString(),
    type,
    activityPubObject: post,
    summary: post.summary || null,
    content: post.content || post.name || null,
    published: post.published || new Date(),
    attachment: post.attachment,
    sensitive: post.sensitive ?? false,
    replyCount: collectionResult.counts.replyCount,
    likeCount: collectionResult.counts.likeCount,
    shareCount: collectionResult.counts.shareCount,
    customReactions: null,
    visibility: getVisibilityFromPost(post),
    inReplyTo: post.inReplyTo ? getUrlFromAPObjectSafe(post.inReplyTo) : null,
    quoteOf: resolveQuoteUrl(post),
    questionData: narrowByType<Question>(post, ['Question']) ? {
      anyOf: post.anyOf,
      oneOf: post.oneOf,
      closed: post.closed,
      endTime: post.endTime,
      votersCount: post.votersCount,
    } : null
  }
}

// Quote resolution moved to utils/quotes.ts

// Visibility functions moved to utils/visibility.ts
