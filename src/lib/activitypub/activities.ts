import type { Activity, Create, Update, Delete, Follow, Accept, Reject, Like, Announce, Undo, Block, Flag, Add, Remove } from '$lib/activitypub/types';
import { fetchAndProcessAPObject } from '$lib/activitypub/utils/fetch';
import {
  isCreateActivity,
  processActivitySafely,
  ActivityProcessors,
  getActivitySummary,
  processActivityObjects
} from '$lib/activitypub/utils/activities';
import { FetchError, withErrorRecovery } from '$lib/activitypub/utils/errors';
import { importPost, importPostWithAuth, deletePost } from '$lib/activitypub/posts';
import { processFollow, processAccept, deleteFollow } from '$lib/activitypub/follows';
import { processBlock, removeBlock } from '$lib/activitypub/blocks';
import { processAdd, processRemove } from '$lib/activitypub/collections';
import { processLike as processLikeReaction, processAnnounce as processAnnounceReaction, removeLike, removeAnnounce } from '$lib/activitypub/reactions';
import { getUrlFromAPObjectRequired } from '$lib/activitypub/utils/links';

export async function processActivity(url: string): Promise<Activity> {
  // Fetch activity with error recovery and validation
  const fetchResult = await withErrorRecovery(async () => {
    const result = await fetchAndProcessAPObject<Activity>(url, {
      timeout: 10000
    });

    if (!result.success) {
      throw new FetchError(
        `Failed to fetch activity: ${result.error}`,
        'ACTIVITY_FETCH_ERROR',
        { url, status: result.statusCode, error: result.error }
      );
    }

    return result.data!;
  }, { maxRetries: 2, retryDelay: 1000 });

  if (!fetchResult.success) {
    throw fetchResult.error || new FetchError(url);
  }

  const rawActivity = fetchResult.result!;

  // Use the new function to process the fetched activity
  return await processActivityObject(rawActivity);
}

/**
 * Process activity based on its type
 */
async function processActivityByType(activity: Activity): Promise<void> {
  const summary = getActivitySummary(activity);
  const type = summary.type;

  switch (type) {
    case 'Create':
      await processCreateActivity(activity as Create);
      break;
    case 'Update':
      await processUpdateActivity(activity as Update);
      break;
    case 'Delete':
      await processDeleteActivity(activity as Delete);
      break;
    case 'Follow':
      await processFollowActivity(activity as Follow);
      break;
    case 'Accept':
      await processAcceptActivity(activity as Accept);
      break;
    case 'Reject':
      await processRejectActivity(activity as Reject);
      break;
    case 'Like':
      await processLikeActivity(activity as Like);
      break;
    case 'Announce':
      await processAnnounceActivity(activity as Announce);
      break;
    case 'Undo':
      await processUndoActivity(activity as Undo);
      break;
    case 'Block':
      await processBlockActivity(activity as Block);
      break;
    case 'Flag':
      await processFlagActivity(activity as Flag);
      break;
    case 'Add':
      await processAddActivity(activity as Add);
      break;
    case 'Remove':
      await processRemoveActivity(activity as Remove);
      break;
    default:
      console.warn(`Unsupported activity type: ${type}`);
  }
}

/**
 * Process Create activity with improved error handling
 */
async function processCreateActivity(activity: Create): Promise<void> {
  const result = await ActivityProcessors.processCreate(activity, async (url: string) => {
    try {
      await importPost(url);
    } catch (error) {
      console.warn(`Failed to import post from ${url}:`, error);
      throw error; // Re-throw to be handled by the processor
    }
  });

  if (result.failed > 0) {
    console.warn(`Create activity processing completed with ${result.failed} failures:`, result.errors);
  }

  console.log(`Successfully processed ${result.processed} objects from Create activity`);
}

/**
 * Process Update activity
 */
async function processUpdateActivity(activity: Update): Promise<void> {
  const result = await ActivityProcessors.processUpdate(activity, async (url: string) => {
    try {
      await importPost(url);
    } catch (error) {
      console.warn(`Failed to update post from ${url}:`, error);
      throw error;
    }
  });

  if (result.failed > 0) {
    console.warn(`Update activity processing completed with ${result.failed} failures:`, result.errors);
  }

  console.log(`Successfully processed ${result.processed} objects from Update activity`);
}

/**
 * Process Delete activity
 */
async function processDeleteActivity(activity: Delete): Promise<void> {
  const result = await ActivityProcessors.processDelete(activity, async (url: string) => {
    try {
      const deleted = await deletePost(url);
      if (!deleted) {
        console.warn(`Post not found or already deleted: ${url}`);
      }
    } catch (error) {
      console.warn(`Failed to delete post from ${url}:`, error);
      throw error;
    }
  });

  if (result.failed > 0) {
    console.warn(`Delete activity processing completed with ${result.failed} failures:`, result.errors);
  }

  console.log(`Successfully processed ${result.processed} objects from Delete activity`);
}

/**
 * Process Follow activity
 */
async function processFollowActivity(activity: Follow): Promise<void> {
  try {
    // Process the follow activity directly
    const success = await processFollow(activity);

    if (success) {
      console.log(`Successfully processed Follow activity from ${activity.actor} to ${activity.object}`);
    } else {
      console.warn(`Failed to process Follow activity from ${activity.actor} to ${activity.object}`);
    }
  } catch (error) {
    console.error(`Error processing Follow activity:`, error);
    throw error;
  }
}

/**
 * Process Accept activity
 */
async function processAcceptActivity(activity: Accept): Promise<void> {
  try {
    // Process the accept activity directly
    const success = await processAccept(activity);

    if (success) {
      console.log(`Successfully processed Accept activity from ${activity.actor} for ${activity.object}`);
    } else {
      console.warn(`Failed to process Accept activity from ${activity.actor} for ${activity.object}`);
    }
  } catch (error) {
    console.error(`Error processing Accept activity:`, error);
    throw error;
  }
}

/**
 * Process Reject activity
 */
async function processRejectActivity(activity: Reject): Promise<void> {
  const result = await processActivityObjects(activity, async (url: string) => {
    try {
      // TODO: Implement reject logic
      console.log(`Processing reject for: ${url}`);
    } catch (error) {
      console.warn(`Failed to process reject for ${url}:`, error);
      throw error;
    }
  });

  if (result.failed > 0) {
    console.warn(`Reject activity processing completed with ${result.failed} failures:`, result.errors);
  }

  console.log(`Successfully processed ${result.processed} objects from Reject activity`);
}

/**
 * Process Like activity
 */
async function processLikeActivity(activity: Like): Promise<void> {
  try {
    // First, ensure the target post is imported
    const result = await ActivityProcessors.processLike(activity, async (url: string) => {
      try {
        await importPost(url);
      } catch (error) {
        console.warn(`Failed to import post for like from ${url}:`, error);
        throw error;
      }
    });

    if (result.failed > 0) {
      console.warn(`Like activity post import completed with ${result.failed} failures:`, result.errors);
    }

    // Then, create the reaction record
    const success = await processLikeReaction(activity);
    if (success) {
      console.log(`Successfully processed Like activity from ${activity.actor}`);
    } else {
      console.warn(`Failed to process Like activity from ${activity.actor}`);
    }
  } catch (error) {
    console.error(`Error processing Like activity:`, error);
    throw error;
  }
}

/**
 * Process Announce activity
 */
async function processAnnounceActivity(activity: Announce): Promise<void> {
  try {
    // First, ensure the target post is imported
    const result = await ActivityProcessors.processAnnounce(activity, async (url: string) => {
      try {
        await importPost(url);
      } catch (error) {
        console.warn(`Failed to import post for announce from ${url}:`, error);
        throw error;
      }
    });

    if (result.failed > 0) {
      console.warn(`Announce activity post import completed with ${result.failed} failures:`, result.errors);
    }

    // Then, create the reaction record
    const success = await processAnnounceReaction(activity);
    if (success) {
      console.log(`Successfully processed Announce activity from ${activity.actor}`);
    } else {
      console.warn(`Failed to process Announce activity from ${activity.actor}`);
    }
  } catch (error) {
    console.error(`Error processing Announce activity:`, error);
    throw error;
  }
}

/**
 * Process Undo activity
 */
async function processUndoActivity(activity: Undo): Promise<void> {
  try {
    if (!activity.actor || !activity.object) {
      console.warn('Undo activity missing actor or object');
      return;
    }

    const actorUri = getUrlFromAPObjectRequired(activity.actor, 'actor');

    // The object can be either a URL or an embedded activity
    let undoTarget: any;
    if (typeof activity.object === 'string') {
      // If it's a URL, fetch the activity
      try {
        const fetchResult = await fetchAndProcessAPObject(activity.object);
        if (!fetchResult.success) {
          console.warn(`Failed to fetch undo target: ${activity.object}`);
          return;
        }
        undoTarget = fetchResult.data;
      } catch (error) {
        console.warn(`Error fetching undo target ${activity.object}:`, error);
        return;
      }
    } else {
      // If it's an embedded object, use it directly
      undoTarget = activity.object;
    }

    if (!undoTarget || !undoTarget.type) {
      console.warn('Invalid undo target object');
      return;
    }

    // Process based on the type of activity being undone
    switch (undoTarget.type) {
      case 'Like':
        await processUndoLike(actorUri, undoTarget);
        break;
      case 'Announce':
        await processUndoAnnounce(actorUri, undoTarget);
        break;
      case 'Follow':
        await processUndoFollow(actorUri, undoTarget);
        break;
      case 'Block':
        await processUndoBlock(actorUri, undoTarget);
        break;
      default:
        console.warn(`Unsupported undo target type: ${undoTarget.type}`);
    }

    console.log(`Successfully processed Undo activity for ${undoTarget.type} from ${actorUri}`);
  } catch (error) {
    console.error(`Error processing Undo activity:`, error);
    throw error;
  }
}

/**
 * Process Block activity
 */
async function processBlockActivity(activity: Block): Promise<void> {
  try {
    const success = await processBlock(activity);
    if (success) {
      console.log(`Successfully processed Block activity from ${activity.actor} blocking ${activity.object}`);
    }
  } catch (error) {
    console.error(`Error processing Block activity:`, error);
    throw error;
  }
}

/**
 * Process Flag activity
 */
async function processFlagActivity(activity: Flag): Promise<void> {
  const result = await processActivityObjects(activity, async (url: string) => {
    try {
      // TODO: Implement flag/report logic
      console.log(`Processing flag for: ${url}`);
    } catch (error) {
      console.warn(`Failed to process flag for ${url}:`, error);
      throw error;
    }
  });

  if (result.failed > 0) {
    console.warn(`Flag activity processing completed with ${result.failed} failures:`, result.errors);
  }

  console.log(`Successfully processed ${result.processed} objects from Flag activity`);
}

/**
 * Helper functions for processing Undo activities
 */

/**
 * Process Undo Like activity
 */
async function processUndoLike(actorUri: string, likeActivity: any): Promise<void> {
  try {
    if (!likeActivity.object) {
      console.warn('Like activity missing object');
      return;
    }

    const objectUri = getUrlFromAPObjectRequired(likeActivity.object, 'object');
    const success = await removeLike(actorUri, objectUri);

    if (success) {
      console.log(`Successfully undid Like: ${actorUri} -> ${objectUri}`);
    } else {
      console.warn(`Failed to undo Like: ${actorUri} -> ${objectUri}`);
    }
  } catch (error) {
    console.error('Error processing Undo Like:', error);
    throw error;
  }
}

/**
 * Process Undo Announce activity
 */
async function processUndoAnnounce(actorUri: string, announceActivity: any): Promise<void> {
  try {
    if (!announceActivity.object) {
      console.warn('Announce activity missing object');
      return;
    }

    const objectUri = getUrlFromAPObjectRequired(announceActivity.object, 'object');
    const success = await removeAnnounce(actorUri, objectUri);

    if (success) {
      console.log(`Successfully undid Announce: ${actorUri} -> ${objectUri}`);
    } else {
      console.warn(`Failed to undo Announce: ${actorUri} -> ${objectUri}`);
    }
  } catch (error) {
    console.error('Error processing Undo Announce:', error);
    throw error;
  }
}

/**
 * Process Undo Follow activity
 */
async function processUndoFollow(actorUri: string, followActivity: any): Promise<void> {
  try {
    if (!followActivity.object) {
      console.warn('Follow activity missing object');
      return;
    }

    const objectUri = getUrlFromAPObjectRequired(followActivity.object, 'object');
    const success = await deleteFollow(actorUri, objectUri);

    if (success) {
      console.log(`Successfully undid Follow: ${actorUri} -> ${objectUri}`);
    } else {
      console.warn(`Failed to undo Follow: ${actorUri} -> ${objectUri}`);
    }
  } catch (error) {
    console.error('Error processing Undo Follow:', error);
    throw error;
  }
}

/**
 * Process Undo Block activity
 */
async function processUndoBlock(actorUri: string, blockActivity: any): Promise<void> {
  try {
    if (!blockActivity.object) {
      console.warn('Block activity missing object');
      return;
    }

    const objectUri = getUrlFromAPObjectRequired(blockActivity.object, 'object');
    const success = await removeBlock(actorUri, objectUri);

    if (success) {
      console.log(`Successfully undid Block: ${actorUri} -> ${objectUri}`);
    } else {
      console.warn(`Failed to undo Block: ${actorUri} -> ${objectUri}`);
    }
  } catch (error) {
    console.error('Error processing Undo Block:', error);
    throw error;
  }
}

/**
 * Process Add activity
 */
async function processAddActivity(activity: Add): Promise<void> {
  try {
    const success = await processAdd(activity);
    if (success) {
      console.log(`Successfully processed Add activity from ${activity.actor} adding ${activity.object} to ${activity.target}`);
    }
  } catch (error) {
    console.error(`Error processing Add activity:`, error);
    throw error;
  }
}

/**
 * Process Remove activity
 */
async function processRemoveActivity(activity: Remove): Promise<void> {
  try {
    const success = await processRemove(activity);
    if (success) {
      console.log(`Successfully processed Remove activity from ${activity.actor} removing ${activity.object} from ${activity.target}`);
    }
  } catch (error) {
    console.error(`Error processing Remove activity:`, error);
    throw error;
  }
}

/**
 * Validate activity before processing (public API)
 */
export function validateActivity(activity: any): { isValid: boolean; errors: string[] } {
  const result = processActivitySafely(activity);

  if (result.success) {
    return { isValid: true, errors: [] };
  }

  return {
    isValid: false,
    errors: [result.error.message]
  };
}

/**
 * Get activity type safely
 */
export function getActivityType(activity: any): string | null {
  try {
    const result = processActivitySafely(activity);
    if (result.success) {
      return getActivitySummary(result.activity).type;
    }
    return null;
  } catch {
    return null;
  }
}

/**
 * Check if an object is a supported activity type
 */
export function isSupportedActivity(obj: any): boolean {
  const result = processActivitySafely(obj);
  return result.success;
}

/**
 * Process multiple activities in batch
 */
export async function processActivities(
  urls: string[],
  options: {
    parallel?: boolean;
    maxConcurrency?: number;
    continueOnError?: boolean;
  } = {}
): Promise<{
  processed: Activity[];
  failed: string[];
  errors: string[];
}> {
  const { parallel = true, maxConcurrency = 3, continueOnError = true } = options;

  const processed: Activity[] = [];
  const failed: string[] = [];
  const errors: string[] = [];

  if (parallel) {
    // Process in batches
    const batches: string[][] = [];
    for (let i = 0; i < urls.length; i += maxConcurrency) {
      batches.push(urls.slice(i, i + maxConcurrency));
    }

    for (const batch of batches) {
      const results = await Promise.allSettled(
        batch.map(url => processActivity(url))
      );

      for (let i = 0; i < results.length; i++) {
        const result = results[i];
        const url = batch[i];

        if (result.status === 'fulfilled') {
          processed.push(result.value);
        } else {
          failed.push(url);
          errors.push(`${url}: ${result.reason}`);

          if (!continueOnError) {
            return { processed, failed, errors };
          }
        }
      }
    }
  } else {
    // Sequential processing
    for (const url of urls) {
      try {
        const activity = await processActivity(url);
        processed.push(activity);
      } catch (error) {
        failed.push(url);
        errors.push(`${url}: ${error instanceof Error ? error.message : 'Unknown error'}`);

        if (!continueOnError) {
          break;
        }
      }
    }
  }

  return { processed, failed, errors };
}

/**
 * Process an already-fetched Activity object
 */
export async function processActivityObject(activity: Activity): Promise<Activity> {
  // Validate activity structure
  const activityResult = processActivitySafely(activity);
  if (!activityResult.success) {
    throw activityResult.error;
  }

  const validatedActivity = activityResult.activity;

  // Log activity summary for debugging
  const summary = getActivitySummary(validatedActivity);
  console.log(`Processing ${summary.type} activity from ${summary.actor} with ${summary.objectCount} objects`);

  // Process activity based on type
  try {
    await processActivityByType(validatedActivity);
  } catch (error) {
    console.error(`Failed to process ${summary.type} activity:`, error);
    // Don't throw here - we want to return the activity even if processing fails
  }

  return validatedActivity;
}
